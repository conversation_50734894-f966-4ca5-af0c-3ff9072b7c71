package session

import (
	"context"
	"errors"
	"fmt"
	"math"
	"sort"
	"time"

	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/consts"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
	"gorm.io/gorm"
)

// Repository defines the interface for session-related database operations
type Repository interface {
	CreateSession(ctx context.Context, session entities.Session) (entities.Session, error)
	GetSessionByID(ctx context.Context, id uuid.UUID) (entities.Session, error)
	GetSessionByRegID(ctx context.Context, regID string) (entities.Session, error)
	// UpdateSession updates a session
	UpdateSession(ctx context.Context, session entities.Session) error
	// DeleteSession deletes a session
	DeleteSession(ctx context.Context, id uuid.UUID) error
	// ListSessions lists all sessions with optional filtering
	ListSessions(ctx context.Context, status string, limit, offset int) ([]entities.Session, int64, error)
	// RecordSessionEvent records a new session event
	RecordSessionEvent(ctx context.Context, event entities.SessionEvent) error
	// GetSessionEvents retrieves events for a session
	GetSessionEvents(ctx context.Context, sessionID uuid.UUID, limit, offset int) ([]entities.SessionEvent, error)
	// CreateSubscription creates a new subscription
	CreateSubscription(ctx context.Context, subscription entities.SessionSubscription) error
	// GetSubscriptions retrieves subscriptions for a session
	GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error)
	// UpdateSubscription updates a subscription
	UpdateSubscription(ctx context.Context, subscription entities.SessionSubscription) error
	// DeleteSubscription deletes a subscription
	DeleteSubscription(ctx context.Context, id uuid.UUID) error
	GetSubscriptionBySessionAndPhone(ctx context.Context, sessionID uuid.UUID, phone string) (entities.SessionSubscription, error)
	GetPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error)
	GetCurrentPresences(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceResponse, error)
	GetLastPresenceByPhone(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceItem, error)
	GetPresenceHistory(ctx context.Context, sessionID uuid.UUID, req dtos.PresenceHistoryReq) (dtos.PresenceHistoryResponse, error)
	FindActiveDeviceByRegID(ctx context.Context, regID string) (dtos.DeviceIsActive, error)
	CleanWhatsmeowTables(ctx context.Context, ourJID string) error
	SubscribeIsExist(ctx context.Context, sessionId, phone string) bool
	GetConnectedSessions(ctx context.Context) ([]entities.Session, error)
	CheckJIDExistsInWhatsmeowDevice(ctx context.Context, jid string) (bool, error)
	CleanupOrphanedSession(ctx context.Context, sessionID uuid.UUID) error
	// GetUncheckedPresences gets all unchecked presence records
	GetUncheckedPresences(ctx context.Context) ([]entities.Presence, error)
	// MarkPresencesAsChecked marks presence records as checked
	MarkPresencesAsChecked(ctx context.Context, presenceIDs []uuid.UUID) error
	// DeleteDuplicatePresences deletes duplicate presence records
	DeleteDuplicatePresences(ctx context.Context, presenceIDs []uuid.UUID) error
	GetSessionSubscription(ctx context.Context, sessionId, phone string) (entities.SessionSubscription, error)
}

type sessionRepository struct {
	db *gorm.DB
}

// NewSessionRepository creates a new session repository
func NewRepository(db *gorm.DB) Repository {
	return &sessionRepository{
		db: db,
	}
}

func (r *sessionRepository) CreateSession(ctx context.Context, session entities.Session) (entities.Session, error) {
	err := r.db.WithContext(ctx).Create(&session).Error
	return session, err
}

func (r *sessionRepository) GetSessionByID(ctx context.Context, id uuid.UUID) (entities.Session, error) {
	var session entities.Session
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&session).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return session, errors.New("session not found")
		}
		return session, err
	}
	return session, nil
}

func (r *sessionRepository) GetSessionByRegID(ctx context.Context, regID string) (entities.Session, error) {
	var session entities.Session
	err := r.db.WithContext(ctx).Where("reg_id = ?", regID).First(&session).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return session, errors.New("session not found")
		}
		return session, err
	}
	return session, nil
}

func (r *sessionRepository) UpdateSession(ctx context.Context, session entities.Session) error {
	return r.db.WithContext(ctx).Save(&session).Error
}

func (r *sessionRepository) DeleteSession(ctx context.Context, id uuid.UUID) error {

	return r.db.WithContext(ctx).Delete(&entities.Session{}, id).Error
}

func (r *sessionRepository) ListSessions(ctx context.Context, status string, limit, offset int) ([]entities.Session, int64, error) {
	var sessions []entities.Session
	var count int64

	query := r.db.WithContext(ctx).Model(&entities.Session{})
	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	if limit > 0 {
		query = query.Limit(limit).Offset(offset)
	}

	err = query.Order("created_at DESC").Find(&sessions).Error
	return sessions, count, err
}

func (r *sessionRepository) RecordSessionEvent(ctx context.Context, event entities.SessionEvent) error {
	event.Timestamp = time.Now()
	return r.db.WithContext(ctx).Create(&event).Error
}

func (r *sessionRepository) GetSessionEvents(ctx context.Context, sessionID uuid.UUID, limit, offset int) ([]entities.SessionEvent, error) {
	var events []entities.SessionEvent
	query := r.db.WithContext(ctx).Where("session_id = ?", sessionID)

	if limit > 0 {
		query = query.Limit(limit).Offset(offset)
	}

	err := query.Order("timestamp DESC").Find(&events).Error
	return events, err
}

func (r *sessionRepository) CreateSubscription(ctx context.Context, subscription entities.SessionSubscription) error {
	return r.db.WithContext(ctx).Create(&subscription).Error
}

func (r *sessionRepository) GetSubscriptions(ctx context.Context, sessionID uuid.UUID) ([]entities.SessionSubscription, error) {
	var subscriptions []entities.SessionSubscription
	err := r.db.WithContext(ctx).Where("session_id = ?", sessionID).Find(&subscriptions).Error
	return subscriptions, err
}

func (r *sessionRepository) UpdateSubscription(ctx context.Context, subscription entities.SessionSubscription) error {
	return r.db.WithContext(ctx).Updates(&subscription).Error
}

func (r *sessionRepository) DeleteSubscription(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&entities.SessionSubscription{}, id).Error
}

func (r *sessionRepository) GetSubscriptionBySessionAndPhone(ctx context.Context, sessionID uuid.UUID, phone string) (entities.SessionSubscription, error) {
	var subscription entities.SessionSubscription
	err := r.db.WithContext(ctx).
		Where("session_id = ? AND phone = ?", sessionID, phone).
		First(&subscription).Error
	return subscription, err
}

func (r *sessionRepository) GetPresences(ctx context.Context, sessionID uuid.UUID, page, perPage int) (dtos.PaginatedData, error) {
	var presences []entities.Presence
	var count int64

	// First, get all subscriptions for this session
	var subscriptions []entities.SessionSubscription
	err := r.db.WithContext(ctx).Where("session_id = ?", sessionID).Find(&subscriptions).Error
	if err != nil {
		return dtos.PaginatedData{}, err
	}

	// If no subscriptions, return empty result
	if len(subscriptions) == 0 {
		return dtos.PaginatedData{
			Rows:       []entities.Presence{},
			Total:      0,
			Page:       int64(page),
			PerPage:    int64(perPage),
			TotalPages: 0,
		}, nil
	}

	// Get the session to find the reg_id
	var session entities.Session
	err = r.db.WithContext(ctx).Where("id = ?", sessionID).First(&session).Error
	if err != nil {
		return dtos.PaginatedData{}, err
	}

	// Extract phone numbers from subscriptions
	var phones []string
	for _, sub := range subscriptions {
		phones = append(phones, sub.Phone)
	}

	// Query presences for these phones and this session's reg_id
	query := r.db.WithContext(ctx).Model(&entities.Presence{}).
		Where("session_id = ?", session.ID).
		Where("subscribe_phone IN ?", phones).
		Order("created_at DESC")

	// Count total records
	err = query.Count(&count).Error
	if err != nil {
		return dtos.PaginatedData{}, err
	}

	// Apply pagination
	offset := (page - 1) * perPage
	if perPage > 0 {
		query = query.Limit(perPage).Offset(offset)
	}

	// Get the presences
	err = query.Find(&presences).Error
	if err != nil {
		return dtos.PaginatedData{}, err
	}

	var dtoItems = r.calculateOnlineDurations(presences, session.Timezone)
	var groupByPhone = r.groupByPhone(dtoItems)

	// Calculate total online time for each phone group
	for i, phoneGroup := range groupByPhone {
		// For GetPresences, we calculate total online time for all time (no time range limit)
		totalOnlineTime, err := r.calculateSinglePhoneOnlineTime(ctx, sessionID, phoneGroup.Phone, time.Time{}, time.Now())
		if err != nil {
			totalOnlineTime = 0
		}
		groupByPhone[i].TotalOnlineTime = totalOnlineTime
	}

	return dtos.PaginatedData{
		Rows:       groupByPhone,
		Total:      count,
		Page:       int64(page),
		PerPage:    int64(perPage),
		TotalPages: int(math.Ceil(float64(count) / float64(perPage))),
	}, nil
}

func (r *sessionRepository) FindActiveDeviceByRegID(ctx context.Context, regID string) (dtos.DeviceIsActive, error) {
	var device dtos.DeviceIsActive
	err := r.db.WithContext(ctx).Select("jid as j_id, registration_id, platform, push_name").Table("whatsmeow_device").Where("registration_id = ?", regID).Find(&device).Error
	return device, err
}

// ...existing code...

// CleanWhatsmeowTables removes data from Whatsmeow tables for a specific JID
func (r *sessionRepository) CleanWhatsmeowTables(ctx context.Context, ourJID string) error {
	// Begin a transaction to ensure all deletions are performed atomically
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// Delete from whatsapp_contacts table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_contacts WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from whatsmeow_message_secrets table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_message_secrets WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from whatsmeow_sessions table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_sessions WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from whatsmeow_sender_keys table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_sender_keys WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from whatsmeow_privacy_tokens table
	if err := tx.Debug().Exec("DELETE FROM whatsmeow_privacy_tokens WHERE our_jid = ?", ourJID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit the transaction
	return tx.Commit().Error
}

// ...existing code...

// CleanSession removes data related to a session from presences and session_subscriptions tables
func (r *sessionRepository) CleanSession(ctx context.Context, id string) error {
	// Begin a transaction to ensure all deletions are performed atomically
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// First, find the session ID from the reg_id
	var session entities.Session
	if err := tx.WithContext(ctx).Where("id = ?", id).First(&session).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("session not found")
		}
		return err
	}

	// Delete from presences table based on reg_id
	if err := tx.Exec("DELETE FROM presences WHERE session_id = ?", session.ID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Delete from session_subscriptions table using the session ID
	if err := tx.Exec("DELETE FROM session_subscriptions WHERE session_id = ?", session.ID).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit the transaction
	err := tx.Commit().Error

	r.CleanWhatsmeowTables(ctx, session.JID)
	return err

}

func (r *sessionRepository) GetCurrentPresences(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceResponse, error) {
	var response dtos.CurrentPresenceResponse
	response.SessionID = sessionID
	response.UpdatedAt = time.Now()

	// Get the session to find the reg_id
	var session entities.Session
	err := r.db.WithContext(ctx).Where("id = ?", sessionID).First(&session).Error
	if err != nil {
		return response, err
	}

	var presences []entities.Presence

	// If phone parameter is provided, get presence for that specific phone
	if phone != "" {
		err = r.db.WithContext(ctx).
			Where("session_id = ? AND subscribe_phone = ?", session.ID, phone).
			Order("created_at DESC").
			Limit(1).
			Find(&presences).Error
		if err != nil {
			return response, err
		}
	} else {
		// Original logic: get all subscriptions for this session
		var subscriptions []entities.SessionSubscription
		err := r.db.WithContext(ctx).Where("session_id = ?", sessionID).Find(&subscriptions).Error
		if err != nil {
			return response, err
		}

		// If no subscriptions, return empty result
		if len(subscriptions) == 0 {
			response.Presences = []dtos.CurrentPresenceItem{}
			return response, nil
		}

		// Extract phone numbers from subscriptions
		var phones []string
		for _, sub := range subscriptions {
			phones = append(phones, sub.Phone)
		}

		// Get the latest presence for each phone
		subQuery := r.db.WithContext(ctx).Model(&entities.Presence{}).
			Select("subscribe_phone, MAX(created_at) as max_created_at").
			Where("session_id = ?", session.ID).
			Where("subscribe_phone IN ?", phones).
			Group("subscribe_phone")

		err = r.db.WithContext(ctx).
			Table("presences").
			Select("presences.*").
			Joins("INNER JOIN (?) as latest ON presences.subscribe_phone = latest.subscribe_phone AND presences.created_at = latest.max_created_at", subQuery).
			Where("presences.session_id = ?", session.ID).
			Find(&presences).Error

		if err != nil {
			return response, err
		}
	}

	// Convert to response format
	response.Presences = make([]dtos.CurrentPresenceItem, len(presences))
	for i, presence := range presences {
		response.Presences[i] = dtos.CurrentPresenceItem{
			Phone:     presence.SubscribePhone,
			Status:    presence.Status,
			UpdatedAt: utils.ConvertTimeToTimezone(presence.CreatedAt, session.Timezone),
		}
		if !presence.LastSeen.IsZero() {
			convertedLastSeen := utils.ConvertTimeToTimezone(presence.LastSeen, session.Timezone)
			response.Presences[i].LastSeen = &convertedLastSeen
		}
	}

	return response, nil
}

func (r *sessionRepository) GetLastPresenceByPhone(ctx context.Context, sessionID uuid.UUID, phone string) (dtos.CurrentPresenceItem, error) {
	var (
		current_session entities.Session
		last_presences  []entities.Presence
		response        dtos.CurrentPresenceItem
	)

	r.db.WithContext(ctx).
		Where("id = ?", sessionID).
		First(&current_session)

	if current_session.ID == uuid.Nil {
		return response, errors.New("session not found")
	}

	if phone == "" {
		return response, errors.New("phone not found")
	}

	response.Phone = phone
	response.UpdatedAt = time.Now()

	if err := r.db.WithContext(ctx).
		Where("session_id = ? AND subscribe_phone = ?", current_session.ID, phone).
		Order("created_at DESC").
		Limit(1).
		Find(&last_presences).Error; err != nil && err != gorm.ErrRecordNotFound {
		return response, err
	}

	if len(last_presences) == 0 {
		return response, errors.New("presence not found")
	}

	if last_presences[0].Status == consts.PresenceOnline {
		response.Status = consts.PresenceOnline
		response.LastSeen = nil
		return response, nil
	} else if last_presences[0].Status == consts.PresenceOffline {
		response.Status = consts.PresenceOffline
		response.LastSeen = &last_presences[0].LastSeen
		return response, nil
	} else {
		response.Status = "unknown"
		response.LastSeen = nil
		return response, errors.New("presence status is not online or offline")
	}
}

func (r *sessionRepository) GetPresenceHistory(ctx context.Context, sessionID uuid.UUID, req dtos.PresenceHistoryReq) (dtos.PresenceHistoryResponse, error) {
	var response dtos.PresenceHistoryResponse
	response.SessionID = sessionID
	response.Page = req.Page
	response.PerPage = req.PerPage

	// Set default pagination values if not provided
	if response.Page <= 0 {
		response.Page = 1
	}
	if response.PerPage <= 0 {
		response.PerPage = 10
	}

	if req.StartDate != "" && req.EndDate != "" {
		// Parse custom date range
		startTime, err := time.Parse("2006-01-02T15:04:05.000000", req.StartDate)
		if err != nil {
			return response, fmt.Errorf("invalid start_date format: %v", err)
		}
		endTime, err := time.Parse("2006-01-02T15:04:05.000000", req.EndDate)
		if err != nil {
			return response, fmt.Errorf("invalid end_date format: %v", err)
		}

		response.FromTime = startTime
		response.ToTime = endTime
		response.TimeRange = fmt.Sprintf("custom: %s to %s", req.StartDate, req.EndDate)
	} else {
		// Use time_range if start_date/end_date not provided
		if req.TimeRange == "" {
			return response, errors.New("either time_range or start_date/end_date must be provided")
		}

		response.TimeRange = req.TimeRange
		response.ToTime = time.Now()

		// Calculate the start time based on the time range
		switch req.TimeRange {
		case "3h":
			response.FromTime = response.ToTime.Add(-3 * time.Hour)
		case "24h":
			response.FromTime = response.ToTime.Add(-24 * time.Hour)
		case "2d":
			response.FromTime = response.ToTime.Add(-48 * time.Hour)
		case "3d":
			response.FromTime = response.ToTime.Add(-72 * time.Hour)
		default:
			return response, errors.New("invalid time range")
		}
	}

	// Get the session to find the reg_id
	var session entities.Session
	err := r.db.WithContext(ctx).Debug().Where("id = ?", sessionID).Debug().First(&session).Error
	if err != nil {
		return response, err
	}

	// Build the base query
	query := r.db.WithContext(ctx).
		Model(&entities.Presence{}).Debug().
		Where("session_id = ?", session.ID.String()).
		Where("created_at BETWEEN ? AND ?", response.FromTime, response.ToTime)

	// If phone parameter is provided, filter by that specific phone
	if req.Phone != "" {
		query = query.Debug().Where("subscribe_phone = ?", req.Phone)
	} else {
		// If no phone specified, get all subscriptions for this session
		var subscriptions []entities.SessionSubscription
		err := r.db.WithContext(ctx).Debug().Where("session_id = ?", sessionID).Find(&subscriptions).Error
		if err != nil {
			return response, err
		}

		// If no subscriptions, return empty result
		if len(subscriptions) == 0 {
			response.Presences = []dtos.PresenceHistoryItemByPhone{}
			response.Total = 0
			response.TotalPages = 0
			return response, nil
		}

		// Extract phone numbers from subscriptions
		var phones []string
		for _, sub := range subscriptions {
			phones = append(phones, sub.Phone)
		}

		query = query.Debug().Where("subscribe_phone IN ?", phones)
	}

	// Count total records
	var count int64
	cntQuery := query
	err = cntQuery.Debug().Where("status = ?", consts.PresenceOnline).Count(&count).Error
	if err != nil {
		return response, err
	}
	response.Total = count

	// Calculate total pages
	response.TotalPages = int((count + int64(response.PerPage) - 1) / int64(response.PerPage))

	// Calculate offset for pagination
	offset := (response.Page - 1) * response.PerPage * 2

	// Get the presences with pagination
	var presences []entities.Presence
	err = query.Order("created_at DESC").Debug().
		Offset(offset).
		Limit(response.PerPage).
		Find(&presences).Error
	if err != nil {
		return response, err
	}

	dtoItems := r.calculateOnlineDurations(presences, session.Timezone)
	groupByPhone := r.groupByPhone(dtoItems)

	response.Presences = groupByPhone

	for i, phoneGroup := range response.Presences {
		totalOnlineTime, err := r.calculateSinglePhoneOnlineTime(ctx, sessionID, phoneGroup.Phone, response.FromTime, response.ToTime)
		if err != nil {
			totalOnlineTime = 0
		}

		response.Presences[i].TotalOnlineTime = totalOnlineTime
	}

	// Convert time fields to session timezone
	response.FromTime = utils.ConvertTimeToTimezone(response.FromTime, session.Timezone)
	response.ToTime = utils.ConvertTimeToTimezone(response.ToTime, session.Timezone)

	return response, nil
}

func (r *sessionRepository) calculateOnlineDurations(presences []entities.Presence, timezone string) []dtos.PresenceHistoryItem {
	var dtoItems []dtos.PresenceHistoryItem
	for i, presence := range presences {
		if presence.Status == consts.PresenceOnline {

			if i == 0 && presence.Status == consts.PresenceOnline {
				duration := time.Now().Sub(presence.CreatedAt).Seconds()

				dtoItem := presence.ConvertDto(timezone)
				dtoItem.OnlineDuration = duration
				dtoItem.StartTime = utils.ConvertTimeToTimezone(presence.CreatedAt, timezone)
				dtoItems = append(dtoItems, dtoItem)
				continue
			}

			x := i - 1

			for x < len(presences) {
				if presences[x].Status == consts.PresenceOffline {
					dtoItem := presence.ConvertDto(timezone)

					duration := presences[x].CreatedAt.Sub(presence.CreatedAt).Seconds()
					dtoItem.OnlineDuration = duration
					dtoItem.StartTime = utils.ConvertTimeToTimezone(presence.CreatedAt, timezone)
					dtoItem.EndTime = utils.ConvertTimeToTimezone(presences[x].CreatedAt, timezone)

					//offlineDto := presences[x].ConvertDto()
					dtoItems = append(dtoItems, dtoItem)
					//	dtoItems = append(dtoItems, offlineDto)

					break // İlk Offline bulundu, diğerlerine bakma
				}

				x-- // Offline bulunana kadar devam
			}
		}
	}

	return dtoItems
}

// group []dtos.PresenceHistoryItem by phone"

func (r *sessionRepository) groupByPhone(items []dtos.PresenceHistoryItem) []dtos.PresenceHistoryItemByPhone {

	grouped := make(map[string][]dtos.PresenceHistoryItem)
	for _, item := range items {
		grouped[item.Phone] = append(grouped[item.Phone], item)
	}

	var groupedItems []dtos.PresenceHistoryItemByPhone

	for phone, presences := range grouped {
		sort.Slice(presences, func(i, j int) bool {
			return presences[i].StartTime.After(presences[j].StartTime)
		})

		// Clean up consecutive duplicate presence records from database
		//cleanedPresences := r.cleanupDuplicatePresenceRecords(context.Background(), phone, presences)

		// Create the phone group with cleaned presences
		groupedItems = append(groupedItems, dtos.PresenceHistoryItemByPhone{
			Phone:     phone,
			Presences: presences,
		})
	}

	return groupedItems
}

// cleanupDuplicatePresenceRecords removes consecutive duplicate presence records from database and returns cleaned array
func (r *sessionRepository) cleanupDuplicatePresenceRecords(ctx context.Context, phone string, presences []dtos.PresenceHistoryItem) []dtos.PresenceHistoryItem {
	if len(presences) < 2 {
		return presences
	}

	var idsToDelete []uuid.UUID

	for i := 0; i < len(presences)-1; i++ {
		currentRecord := presences[i]
		nextRecord := presences[i+1]

		// If consecutive records have the same status, mark the older one for deletion
		if currentRecord.Status == nextRecord.Status {
			idsToDelete = append(idsToDelete, nextRecord.ID)
		}
	}

	// Delete the duplicate records if any found
	if len(idsToDelete) > 0 {
		err := r.db.WithContext(ctx).
			Where("id IN ?", idsToDelete).
			Delete(&entities.Presence{}).Error

		if err != nil {
			// Log error but don't fail the main operation
			fmt.Printf("Error deleting duplicate presence records: %v\n", err)
		}
	}

	//remove deleted items to presence array
	for _, id := range idsToDelete {
		for i, presence := range presences {
			if presence.ID == id {
				presences = append(presences[:i], presences[i+1:]...)
				break
			}
		}
	}

	return presences
}

// calculateSinglePhoneOnlineTime calculates online time for a single phone
func (r *sessionRepository) calculateSinglePhoneOnlineTime(ctx context.Context, sessionID uuid.UUID, phone string, fromTime, toTime time.Time) (float64, error) {
	var presences []entities.Presence

	// Get the session to find the reg_id
	var session entities.Session
	err := r.db.WithContext(ctx).Where("id = ?", sessionID).First(&session).Error
	if err != nil {
		return 0, err
	}

	// Get presence records for this specific phone in the time range
	err = r.db.WithContext(ctx).Model(&entities.Presence{}).
		Where("session_id = ?", session.ID).
		Where("subscribe_phone = ?", phone).
		Where("created_at >= ? AND created_at <= ?", fromTime, toTime).
		Order("created_at ASC").
		Find(&presences).Error
	if err != nil {
		return 0, err
	}

	// Calculate online time for this phone
	var totalOnlineTime float64
	var lastOnlineTime *time.Time

	for _, presence := range presences {
		if presence.Status == consts.PresenceOnline {
			// User went online
			lastOnlineTime = &presence.CreatedAt
		} else if presence.Status == consts.PresenceOffline {
			// User went offline
			if lastOnlineTime != nil {
				// Calculate duration between online and offline
				duration := presence.CreatedAt.Sub(*lastOnlineTime).Seconds()
				if duration > 0 {
					totalOnlineTime += duration
				}
				lastOnlineTime = nil
			}
		}
	}

	// Handle user who is still online at the end of the time range
	if lastOnlineTime != nil {
		// User is still online, calculate duration until end of time range
		duration := toTime.Sub(*lastOnlineTime).Seconds()
		if duration > 0 {
			totalOnlineTime += duration
		}
	}

	return totalOnlineTime, nil
}

// GetConnectedSessions gets all sessions with connected status
func (r *sessionRepository) GetConnectedSessions(ctx context.Context) ([]entities.Session, error) {
	var sessions []entities.Session
	err := r.db.WithContext(ctx).Where("status = ?", entities.SessionStatusConnected).Find(&sessions).Error
	return sessions, err
}

// CheckJIDExistsInWhatsmeowDevice checks if JID exists in whatsmeow_device table
func (r *sessionRepository) CheckJIDExistsInWhatsmeowDevice(ctx context.Context, jid string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Table("whatsmeow_device").Where("jid = ?", jid).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// CleanupOrphanedSession cleans up session and its subscriptions using soft delete
func (r *sessionRepository) CleanupOrphanedSession(ctx context.Context, sessionID uuid.UUID) error {
	// Start a transaction
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Soft delete the session using GORM's Delete method
	err := tx.Where("id = ?", sessionID).Delete(&entities.Session{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// Set all session subscriptions to inactive (soft delete subscriptions too)
	err = tx.Where("session_id = ?", sessionID).Delete(&entities.SessionSubscription{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// Commit the transaction
	return tx.Commit().Error
}

// GetUncheckedPresences gets all unchecked presence records
func (r *sessionRepository) GetUncheckedPresences(ctx context.Context) ([]entities.Presence, error) {
	var presences []entities.Presence
	err := r.db.WithContext(ctx).Where("is_checked = ?", false).Order("session_id, subscribe_phone, created_at").Find(&presences).Error
	return presences, err
}

// MarkPresencesAsChecked marks presence records as checked
func (r *sessionRepository) MarkPresencesAsChecked(ctx context.Context, presenceIDs []uuid.UUID) error {
	if len(presenceIDs) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Model(&entities.Presence{}).Where("id IN ?", presenceIDs).Update("is_checked", true).Error
}

// DeleteDuplicatePresences deletes duplicate presence records
func (r *sessionRepository) DeleteDuplicatePresences(ctx context.Context, presenceIDs []uuid.UUID) error {
	if len(presenceIDs) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).Where("id IN ?", presenceIDs).Delete(&entities.Presence{}).Error
}

// calculateOnlineDurations2 calculates the online durations for each phone

func (r *sessionRepository) SubscribeIsExist(ctx context.Context, sessionId, phone string) bool {

	var subscribe entities.SessionSubscription
	_ = r.db.WithContext(ctx).Where("session_id = ? AND phone = ?", sessionId, phone).First(&subscribe).Error
	return subscribe.ID != uuid.Nil
}

func (r *sessionRepository) GetSessionSubscription(ctx context.Context, sessionId, phone string) (entities.SessionSubscription, error) {

	var subscribe entities.SessionSubscription
	err := r.db.WithContext(ctx).Where("session_id = ? AND phone = ?", sessionId, phone).First(&subscribe).Error
	if subscribe.ID != uuid.Nil {
		return subscribe, err
	}
	return subscribe, err
}
