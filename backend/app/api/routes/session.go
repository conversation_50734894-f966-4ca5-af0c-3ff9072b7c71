package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sayeworldevelopment/wp-core/pkg/domains/session"
	wp "github.com/sayeworldevelopment/wp-core/pkg/domains/wp"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	"github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/middleware"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
)

// SessionRoutes registers all session-related routes
func SessionRoutes(r *gin.RouterGroup, s session.Service, wpService wp.Service) {
	r.POST("/session", middleware.XApiKey(), CreateSession(s))
	r.GET("/session/:id", middleware.XApiKey(), GetSession(s))
	r.PUT("/session/:id", middleware.XApiKey(), UpdateSession(s))
	r.DELETE("/session/:id", middleware.XApiKey(), DeleteSession(s))
	r.GET("/sessions", middleware.XApiKey(), ListSessions(s))
	r.POST("/session/:id/connect", middleware.XApiKey(), ConnectSession(s))
	r.POST("/session/:id/disconnect", middleware.XApiKey(), DisconnectSession(s))
	r.POST("/session/:id/pause", middleware.XApiKey(), PauseSession(s))
	r.POST("/session/:id/resume", middleware.XApiKey(), ResumeSession(s))
	r.GET("/session/:id/events", middleware.XApiKey(), GetSessionEvents(s))
	r.POST("/session/:id/subscribe", middleware.XApiKey(), SubscribeSessionPresence(s))
	r.GET("/session/:id/subscriptions", middleware.XApiKey(), GetSessionSubscriptions(s))
	r.GET("/session/:id/presences", middleware.XApiKey(), GetSessionPresences(s))
	r.GET("/session/:id/presence/current", middleware.XApiKey(), GetSessionCurrentPresences(s))
	r.GET("/session/:id/presence/last", middleware.XApiKey(), GetSessionLastPresenceByPhone(s))
	r.GET("/session/:id/presence/history", middleware.XApiKey(), GetSessionPresenceHistory(s))
	r.POST("/session/:id/login-code", middleware.XApiKey(), LoginCodeWithSession(s, wpService))
	r.GET("/session/:id/check-device", middleware.XApiKey(), CheckSessionDevice(s, wpService))
	r.GET("/session/:id/active-device", middleware.XApiKey(), CheckSessionActiveDevice(s, wpService))

	r.POST("/session/:id/profile-photo", middleware.XApiKey(), GetSessionProfilePhoto(s, wpService))
	r.DELETE("/session/:id/subscription/:phone", middleware.XApiKey(), RemoveSessionPresenceSubscription(s))
}

// CreateSession creates a new WhatsApp session
func CreateSession(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateSessionReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Create Session Failed",
				Message: "Create Session Failed, bind json err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Create Session Request",
			Message: "Create Session" + utils.ToJSONString(req),
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		resp, err := s.CreateSession(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Create Session Failed",
				Message: "Create Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Create Session Success",
			Message: "Create Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// GetSession retrieves a session by ID
func GetSession(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Failed",
				Message: "Get Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.GetSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Session Failed",
				Message: "Get Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{
				"error":  err.Error(),
				"status": http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusOK, resp)
	}
}

// UpdateSession updates a session
func UpdateSession(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Session Failed",
				Message: "Update Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		var req dtos.UpdateSessionReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Session Failed",
				Message: "Update Session Failed, bind json err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		req.ID = id
		resp, err := s.UpdateSession(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Update Session Failed",
				Message: "Update Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Update Session Success",
			Message: "Update Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// DeleteSession deletes a session
func DeleteSession(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Delete Session Failed",
				Message: "Delete Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		err = s.DeleteSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Delete Session Failed",
				Message: "Delete Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Delete Session Success",
			Message: "Delete Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, gin.H{
			"message": "Session deleted successfully",
			"status":  http.StatusOK,
		})
	}
}

// ListSessions lists all sessions
func ListSessions(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		status := c.Query("status")
		page := 1
		perPage := 10

		sessions, total, err := s.ListSessions(c, status, page, perPage)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "List Sessions Failed",
				Message: "List Sessions Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"sessions": sessions,
			"total":    total,
			"page":     page,
			"per_page": perPage,
		})
	}
}

// ConnectSession connects a session to WhatsApp
func ConnectSession(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Connect Session Failed",
				Message: "Connect Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.ConnectSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Connect Session Failed",
				Message: "Connect Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Connect Session Success",
			Message: "Connect Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// DisconnectSession disconnects a session from WhatsApp
func DisconnectSession(s session.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Disconnect Session Failed",
				Message: "Disconnect Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		resp, err := s.DisconnectSession(c, id)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Disconnect Session Failed",
				Message: "Disconnect Session Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Disconnect Session Success",
			Message: "Disconnect Session Success",
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, resp)
	}
}

// LoginCodeWithSession gets login code and updates session with reg_id
func LoginCodeWithSession(s session.Service, wpService wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		sessionID, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Login Code Session Failed",
				Message: "Login Code Session Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		var req dtos.GetCodeReq
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Login Code Session Failed",
				Message: "Login Code Session Failed, bind json err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		req.SessionID = sessionID.String()

		log.CreateLog(&entities.Log{
			Title:   "Login Code Session Request",
			Message: "Login Code Session Request" + utils.ToJSONString(req),
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		// Get login code from WhatsApp
		resp, err := wpService.GetCode(c, req)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Login Code Session Failed",
				Message: "Login Code Session Failed, get code err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		// Update session with reg_id
		_, err = s.UpdateSessionWithRegID(c, sessionID, resp.RegId)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Login Code Session Failed",
				Message: "Login Code Session Failed, update session err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Login Code Session Success",
			Message: "Login Code Session Success, session updated with reg_id: " + resp.RegId,
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		// Return both login code response and updated session
		c.JSON(http.StatusOK, gin.H{
			"code":   resp.Code,
			"reg_id": resp.RegId,
			//"session": sessionResp,
		})
	}
}

// CheckSessionDevice checks if a session's device is active and logged in
func CheckSessionDevice(s session.Service, wpService wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		sessionID, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Session Device Failed",
				Message: "Check Session Device Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Get session to retrieve reg_id
		sessionResp, err := s.GetSession(c, sessionID)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Session Device Failed",
				Message: "Check Session Device Failed, get session err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Session not found",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Check if session has reg_id
		if sessionResp.RegID == "" {
			log.CreateLog(&entities.Log{
				Title:   "Check Session Device Failed",
				Message: "Check Session Device Failed, session has no reg_id",
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Session has no registration ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Create CheckDeviceReq with session's reg_id
		req := dtos.CheckDeviceReq{
			RegistrationID: sessionResp.RegID,
		}

		deviceInfo, isLoggedIn := wpService.CheckDevice(c, req)
		if !isLoggedIn {
			log.CreateLog(&entities.Log{
				Title:   "Check Session Device Failed",
				Message: "Check Session Device Failed, device not logged in",
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Device not logged in",
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Check Session Device Success",
			Message: "Check Session Device Success, device is logged in for session: " + utils.ToJSONString(deviceInfo),
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		// Device is logged in, connect the session
		_, err = s.ConnectSession(c, sessionID)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Session Device - Connect Failed",
				Message: "Check Session Device - Connect Failed, err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.JSON(http.StatusOK, gin.H{
				"data":          deviceInfo,
				"session_id":    sessionID.String(),
				"reg_id":        sessionResp.RegID,
				"connected":     false,
				"connect_error": err.Error(),
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Check Session Device Success",
			Message: "Check Session Device Success and Connected for session: " + sessionID.String(),
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, gin.H{
			"data":       deviceInfo,
			"session_id": sessionID.String(),
			"reg_id":     sessionResp.RegID,
			"connected":  true,
		})
	}
}

// CheckSessionDevice checks if a session's device is active and logged in
func CheckSessionActiveDevice(s session.Service, wpService wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		sessionID, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Session Device Failed",
				Message: "Check Session Device Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Get session to retrieve reg_id
		sessionResp, err := s.GetSession(c, sessionID)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Check Session Device Failed",
				Message: "Check Session Device Failed, get session err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Session not found",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Check if session has reg_id
		if sessionResp.RegID == "" {
			log.CreateLog(&entities.Log{
				Title:   "Check Session Device Failed",
				Message: "Check Session Device Failed, session has no reg_id",
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Session has no registration ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		deviceInfo, isLoggedIn := wpService.IsActive(c, sessionResp.RegID)
		if !isLoggedIn {
			log.CreateLog(&entities.Log{
				Title:   "Check Session Device Failed",
				Message: "Check Session Device Failed, device not logged in",
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Device not logged in",
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Check Session Active Device Success",
			Message: "Check Session Active Device Success and Connected for session: " + sessionID.String(),
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, gin.H{
			"data":       deviceInfo,
			"session_id": sessionID.String(),
			"reg_id":     sessionResp.RegID,
			"connected":  true,
		})
	}
}

// GetSessionProfilePhoto gets profile photo for a phone number using session
func GetSessionProfilePhoto(s session.Service, wpService wp.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		sessionID, err := uuid.Parse(idStr)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Profile Photo Failed",
				Message: "Get Profile Photo Failed, invalid UUID:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Invalid session ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Get session to retrieve reg_id
		sessionResp, err := s.GetSession(c, sessionID)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Profile Photo Failed",
				Message: "Get Profile Photo Failed, get session err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Session not found",
				"status": http.StatusBadRequest,
			})
			return
		}

		// Check if session has reg_id
		if sessionResp.RegID == "" {
			log.CreateLog(&entities.Log{
				Title:   "Get Profile Photo Failed",
				Message: "Get Profile Photo Failed, session has no reg_id",
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  "Session has no registration ID",
				"status": http.StatusBadRequest,
			})
			return
		}

		var req struct {
			Phone string `json:"phone"`
		}
		if err := c.ShouldBindJSON(&req); err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Profile Photo Failed",
				Message: "Get Profile Photo Failed, bind json err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		if req.Phone == "" {
			req.Phone = wp.FindNumber(sessionResp.JID)
		}

		//get session subscribe
		subscribe, err := s.GetSessionSubscription(c, sessionID.String(), req.Phone)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Profile Photo Failed",
				Message: "Get Profile Photo Failed, get session subscription err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		if subscribe.ProfileUrl != "" {
			c.JSON(http.StatusOK, gin.H{
				"profile_photo": subscribe.ProfileUrl,
				"session_id":    sessionID.String(),
				"phone":         req.Phone,
			})
			return
		}

		// Get profile photo for the specific phone number using session's reg_id
		profilePhoto, err := wpService.GetProfileByPhone(c, sessionResp.RegID, req.Phone)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Profile Photo Failed",
				Message: "Get Profile Photo Failed, wp service err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		//update subscribe profile url
		subscribe.ProfileUrl = profilePhoto
		err = s.UpdateSubscription(c, subscribe)
		if err != nil {
			log.CreateLog(&entities.Log{
				Title:   "Get Profile Photo Failed",
				Message: "Get Profile Photo Failed, update subscription err:" + err.Error(),
				Entity:  "session",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error":  err.Error(),
				"status": http.StatusBadRequest,
			})
			return
		}

		log.CreateLog(&entities.Log{
			Title:   "Get Profile Photo Success",
			Message: "Get Profile Photo Success for session: " + sessionID.String() + " and phone: " + req.Phone,
			Entity:  "session",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		c.JSON(http.StatusOK, gin.H{
			"profile_photo": profilePhoto,
			"session_id":    sessionID.String(),
			"phone":         req.Phone,
		})
	}
}
